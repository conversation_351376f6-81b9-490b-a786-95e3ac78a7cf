#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
销售数据分析运行脚本
快速运行销售数据分析的简化版本
作者：学文
开发时间：2025-01-16
"""

from sales_data_analyzer import SalesDataAnalyzer
import sys
import os

def main():
    """主函数 - 快速分析"""
    print("="*60)
    print("           销售数据快速分析工具")
    print("="*60)
    
    # 检查文件是否存在
    if not os.path.exists('2.xlsx'):
        print("错误：未找到 2.xlsx 文件！")
        print("请确保文件在当前目录下。")
        return
    
    # 创建分析器
    analyzer = SalesDataAnalyzer('2.xlsx')
    
    # 运行分析
    success = analyzer.run_analysis(generate_charts=False)  # 不生成图表以避免依赖问题
    
    if success:
        print("\n" + "="*60)
        print("           分析完成！可用功能：")
        print("="*60)
        
        # 显示可用的查询选项
        if hasattr(analyzer, 'monthly_analysis') and analyzer.monthly_analysis is not None:
            available_months = analyzer.monthly_analysis.index.get_level_values(0).unique()
            available_products = analyzer.monthly_analysis.index.get_level_values(1).unique()
            
            print(f"\n可查询月份：")
            for i, month in enumerate(available_months, 1):
                print(f"  {i}. {month}")
            
            print(f"\n主要产品系列（前10个）：")
            for i, product in enumerate(available_products[:10], 1):
                print(f"  {i}. {product}")
            
            # 交互式查询示例
            print(f"\n【使用示例】")
            print("# 查询特定月份和产品系列：")
            if len(available_months) > 0 and len(available_products) > 0:
                sample_month = available_months[0]
                sample_product = available_products[0]
                print(f"analyzer.query_specific_data('{sample_month}', '{sample_product}')")
                
                # 执行示例查询
                print(f"\n【示例查询结果】")
                analyzer.query_specific_data(sample_month, sample_product)
            
            print(f"\n# 查询整个月份的数据：")
            if len(available_months) > 0:
                print(f"analyzer.query_specific_data('{available_months[0]}')")
            
            print(f"\n# 重新导出结果：")
            print("analyzer.export_results('我的分析结果.xlsx')")
            
        print(f"\n生成的文件：")
        print("  - 销售分析结果.xlsx (详细数据表)")
        if os.path.exists('销售数据分析图表.png'):
            print("  - 销售数据分析图表.png (可视化图表)")
        
        print(f"\n分析完成！请查看生成的Excel文件获取详细数据。")
        
    else:
        print("分析失败，请检查数据文件格式。")

if __name__ == "__main__":
    main()
