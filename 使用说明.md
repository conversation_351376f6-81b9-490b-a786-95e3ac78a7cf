# 销售数据分析脚本使用说明

## 📋 功能概述

本脚本集合用于分析2.xlsx文件中的销售数据，提供全面的数据透视分析和可视化功能。

## 🚀 快速开始

### 1. 环境要求
```bash
pip install pandas openpyxl matplotlib seaborn numpy
```

### 2. 文件准备
确保以下文件在同一目录下：
- `2.xlsx` (您的销售数据文件)
- `sales_data_analyzer.py` (主分析脚本)
- `run_analysis.py` (快速运行脚本)

### 3. 快速运行
```bash
python run_analysis.py
```

## 📊 主要功能

### 1. 数据处理功能
- ✅ 自动读取Excel文件
- ✅ 处理中文编码问题
- ✅ 数据清洗和验证
- ✅ 日期格式转换和月份分组

### 2. 分析功能
- ✅ 按月份和产品系列进行数据透视
- ✅ 计算销售数量、金额、平均单价
- ✅ 生成交叉统计表
- ✅ 市场占有率分析
- ✅ 客户分析

### 3. 输出功能
- ✅ 控制台详细报告
- ✅ Excel文件导出
- ✅ 可视化图表生成
- ✅ 交互式数据查询

## 🔍 详细使用方法

### 基本分析
```python
from sales_data_analyzer import SalesDataAnalyzer

# 创建分析器
analyzer = SalesDataAnalyzer('2.xlsx')

# 运行完整分析
analyzer.run_analysis()
```

### 查询特定数据
```python
# 查询特定月份和产品系列
analyzer.query_specific_data('2025年07月', 'E3ZC系列')

# 查询整个月份的数据
analyzer.query_specific_data('2025年07月')
```

### 导出自定义结果
```python
# 导出到指定文件
analyzer.export_results('我的分析结果.xlsx')
```

## 📈 输出文件说明

### 1. 销售分析结果.xlsx
包含以下工作表：
- **月度产品分析**: 按月份和产品系列的详细统计
- **月度汇总**: 每月总体销售情况
- **产品系列汇总**: 各产品系列总体表现
- **客户分析**: 客户购买行为分析
- **清洗后数据**: 处理后的原始数据

### 2. 销售数据分析图表.png
包含四个图表：
- 月度销售金额趋势线图
- 产品系列销售占比饼图
- 月度订单数量柱状图
- 月度平均订单金额趋势图

## 📋 输出格式示例

### 控制台输出示例
```
2025年07月销售分析：
  E3ZC系列：
    - 销售数量：150件
    - 销售金额：45,000.00元
    - 平均单价：300.00元
  
  E2E系列：
    - 销售数量：200件
    - 销售金额：60,000.00元
    - 平均单价：300.00元
```

### 数据透视表示例
```
产品系列    2025年04月  2025年05月  2025年06月  2025年07月
E3ZC系列    12000.00   15000.00   18000.00   20000.00
E2E系列     8000.00    9000.00    12000.00   15000.00
```

## 🛠️ 高级功能

### 1. 自定义分析
```python
# 只加载和清洗数据
analyzer.load_data()
analyzer.clean_data()

# 访问清洗后的数据
clean_data = analyzer.clean_data

# 自定义分析
custom_analysis = clean_data.groupby('Brand')['Total_Amount'].sum()
```

### 2. 批量处理
```python
# 处理多个文件
files = ['2.xlsx', '3.xlsx', '4.xlsx']
for file in files:
    analyzer = SalesDataAnalyzer(file)
    analyzer.run_analysis()
    analyzer.export_results(f'分析结果_{file}.xlsx')
```

## ⚠️ 注意事项

### 1. 数据格式要求
- Excel文件必须包含至少8列数据
- 日期列格式：YYYY-MM-DD
- 数量和金额列必须为数值型

### 2. 编码问题
脚本已处理常见的中文编码问题，如遇到问题请检查：
- 文件是否为标准Excel格式
- 列名是否包含特殊字符

### 3. 性能考虑
- 大文件（>10万行）可能需要较长处理时间
- 图表生成需要matplotlib库支持

## 🔧 故障排除

### 常见错误及解决方案

1. **ModuleNotFoundError**
   ```bash
   pip install pandas openpyxl matplotlib seaborn
   ```

2. **文件不存在错误**
   - 检查2.xlsx文件是否在正确目录
   - 确认文件名拼写正确

3. **编码错误**
   - 确保Excel文件为标准格式
   - 尝试重新保存Excel文件

4. **图表显示问题**
   - 安装中文字体支持
   - 在服务器环境下可能需要设置显示后端

## 📞 技术支持

如遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖库版本
3. 数据文件格式
4. 错误日志信息

## 🔄 版本更新

### v1.0 (2025-01-16)
- 基础数据分析功能
- Excel导出功能
- 可视化图表生成
- 交互式查询功能
- 中文编码支持

---

**开发者：学文**  
**开发时间：2025-01-16**  
**模块说明：销售数据透视分析工具**
