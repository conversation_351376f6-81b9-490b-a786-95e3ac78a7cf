# 开发作者: 学文
# 开发时间: 2025-01-16
# 模块说明: 9月份KPI考核表生成工具

import pandas as pd
import numpy as np
from datetime import datetime

def create_kpi_table():
    """创建9月份KPI考核表"""
    
    # KPI数据结构
    kpi_data = {
        '类别': [
            '销售业绩指标', '销售业绩指标', 
            '客户开发指标', '客户开发指标',
            '库存与销售指标', '库存与销售指标', '库存与销售指标', '库存与销售指标', '库存与销售指标',
            '欠款控制指标', '欠款控制指标', '欠款控制指标'
        ],
        '项目名称': [
            '收购龙清单', '收购龙客户数量/万元',
            '新客户开发加人', '收购龙客户数量/万人', 
            '收购龙销售额/万元', '库存个人占比/万元', '产品个人占比/万元', '平台工单处理', '客户投诉',
            '30天欠款', '60天欠款', '90天欠款'
        ],
        '目标值': [
            5, 1.5,
            4, 4,
            72, 2, 2, 2, 2,
            2, 2, 2
        ],
        '实际值': [
            '#待填写#', '#待填写#',
            '#待填写#', '#待填写#',
            '#待填写#', '#待填写#', '#待填写#', '#待填写#', '#待填写#',
            '#待填写#', '#待填写#', '#待填写#'
        ],
        '完成率': [
            '#DIV/0!', '#DIV/0!',
            '#DIV/0!', '#DIV/0!',
            '#DIV/0!', '#DIV/0!', '#DIV/0!', '#DIV/0!', '#DIV/0!',
            '#DIV/0!', '#DIV/0!', '#DIV/0!'
        ],
        '分值': [
            7, 14,
            12, 0,
            15, 2, 2, 2, 2,
            2, 8, 0
        ],
        '得分': [
            '#DIV/0!', '#DIV/0!',
            '#DIV/0!', 0,
            '#DIV/0!', '#DIV/0!', '#DIV/0!', '#DIV/0!', '#DIV/0!',
            0, '#DIV/0!', 0
        ],
        '备注': [
            '基于历史数据设定', '1.5万元合理目标',
            '月均新客户开发', '此项不计分',
            '基于历史72万元+5%增长', '控制库存成本', '产品占比控制', '工单及时处理', '减少客户投诉',
            '30天内回款', '60天内回款', '90天内回款'
        ]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(kpi_data)
    
    return df

def create_excel_kpi_table():
    """创建Excel格式的KPI表格"""
    
    # 创建基础数据
    df = create_kpi_table()
    
    # 保存为Excel文件
    with pd.ExcelWriter('9月KPI考核表.xlsx', engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='KPI考核表', index=False)
        
        # 获取工作表
        worksheet = writer.sheets['KPI考核表']
        
        # 设置列宽
        column_widths = {
            'A': 15,  # 类别
            'B': 20,  # 项目名称
            'C': 10,  # 目标值
            'D': 10,  # 实际值
            'E': 10,  # 完成率
            'F': 8,   # 分值
            'G': 8,   # 得分
            'H': 25   # 备注
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
    
    print("✅ Excel KPI表格已创建: 9月KPI考核表.xlsx")
    return df

def print_kpi_summary():
    """打印KPI汇总信息"""
    
    print("=" * 60)
    print("📊 9月份KPI考核表汇总")
    print("=" * 60)
    print()
    
    print("🎯 主要指标目标:")
    print("  • 销售金额目标: 72万元 (基于历史数据+5%增长)")
    print("  • 新客户开发: 4个")
    print("  • 收购龙清单: 5个")
    print("  • 欠款控制: 各类欠款≤2万元")
    print()
    
    print("📈 分值分布:")
    print("  • 销售业绩指标: 21分 (7+14)")
    print("  • 客户开发指标: 12分 (12+0)")
    print("  • 库存销售指标: 23分 (15+2+2+2+2)")
    print("  • 欠款控制指标: 10分 (2+8+0)")
    print("  • 总分: 66分")
    print()
    
    print("⚠️  填写说明:")
    print("  1. 目标值已设定，基于历史数据分析")
    print("  2. 实际值请在9月底填写")
    print("  3. 完成率 = 实际值/目标值*100%")
    print("  4. 得分 = 完成率*分值/100 (完成率≥100%得满分)")
    print("  5. 欠款控制项目：控制在目标内得分为0是好事")
    print()
    
    print("🎯 建议目标:")
    print("  • 总分≥50分: 合格")
    print("  • 总分≥55分: 良好") 
    print("  • 总分≥60分: 优秀")

if __name__ == "__main__":
    # 创建KPI表格
    df = create_kpi_table()
    
    # 显示表格
    print("📋 9月份KPI考核表")
    print("=" * 100)
    print(df.to_string(index=False))
    print()
    
    # 创建Excel文件
    create_excel_kpi_table()
    
    # 打印汇总信息
    print_kpi_summary()
