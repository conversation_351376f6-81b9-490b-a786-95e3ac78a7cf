#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
- 开发作者: 学文
- 开发时间: 2025-01-15
- 模块说明: 增强型Excel数据清洗汇总脚本，实事求是处理负数数据，确保对账准确性
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime


def load_and_validate_data(file_path='1.xlsx'):
    """
    加载和验证Excel数据文件
    
    Args:
        file_path (str): Excel文件路径
        
    Returns:
        pd.DataFrame: 加载的数据框
        
    Raises:
        FileNotFoundError: 文件不存在
        Exception: 其他读取错误
    """
    print(f"正在读取文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在，请检查文件路径")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"✓ 成功读取文件，共 {len(df)} 行数据，{len(df.columns)} 个字段")
        
        # 验证必要字段是否存在
        required_fields = ['单据日期', '规格型号', '数量', '销售含税单价', '含税总金额']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")
        
        print("✓ 数据字段验证通过")
        return df
        
    except Exception as e:
        raise Exception(f"读取Excel文件时出错: {str(e)}")


def analyze_data_quality(df):
    """
    分析数据质量，识别各种数据问题
    
    Args:
        df (pd.DataFrame): 原始数据框
        
    Returns:
        dict: 数据质量分析结果
    """
    print("正在进行数据质量分析...")
    
    quality_report = {}
    
    # 基础统计
    quality_report['total_rows'] = len(df)
    quality_report['total_columns'] = len(df.columns)
    quality_report['date_range'] = f"{df['单据日期'].min()} 至 {df['单据日期'].max()}"
    
    # 空值分析
    quality_report['null_spec'] = df['规格型号'].isnull().sum()
    quality_report['null_other'] = df[['单据日期', '数量', '销售含税单价', '含税总金额']].isnull().sum().sum()
    
    # 负数分析
    quality_report['negative_qty'] = (df['数量'] < 0).sum()
    quality_report['negative_amount'] = (df['含税总金额'] < 0).sum()
    quality_report['negative_qty_range'] = [df['数量'].min(), df[df['数量'] < 0]['数量'].max()] if quality_report['negative_qty'] > 0 else [0, 0]
    quality_report['negative_amount_range'] = [df['含税总金额'].min(), df[df['含税总金额'] < 0]['含税总金额'].max()] if quality_report['negative_amount'] > 0 else [0, 0]
    
    # 重复数据分析
    quality_report['duplicate_rows'] = df.duplicated().sum()
    quality_report['duplicate_keys'] = df.duplicated(subset=['单据日期', '规格型号']).sum()
    
    # 数据范围分析
    quality_report['unique_dates'] = df['单据日期'].nunique()
    quality_report['unique_specs'] = df['规格型号'].nunique()
    
    # 总金额和数量统计（用于后续验证）
    quality_report['total_amount'] = df['含税总金额'].sum()
    quality_report['total_quantity'] = df['数量'].sum()
    
    print(f"✓ 数据质量分析完成")
    print(f"  - 发现 {quality_report['null_spec']} 个规格型号空值")
    print(f"  - 发现 {quality_report['negative_qty']} 条负数数量记录")
    print(f"  - 发现 {quality_report['negative_amount']} 条负数金额记录")
    print(f"  - 发现 {quality_report['duplicate_keys']} 条重复键值记录")
    
    return quality_report


def clean_data(df, quality_report):
    """
    数据清洗处理：处理空值，保留负数数据
    
    Args:
        df (pd.DataFrame): 原始数据框
        quality_report (dict): 数据质量报告
        
    Returns:
        pd.DataFrame: 清洗后的数据框
        dict: 清洗处理记录
    """
    print("正在进行数据清洗...")
    
    # 创建数据副本避免修改原数据
    cleaned_df = df.copy()
    cleaning_log = {}
    
    # 处理规格型号空值，替换为默认值
    null_spec_count = cleaned_df['规格型号'].isnull().sum()
    if null_spec_count > 0:
        cleaned_df['规格型号'].fillna('AAABBBCCC', inplace=True)
        cleaning_log['spec_null_replaced'] = null_spec_count
        print(f"✓ 已处理 {null_spec_count} 个规格型号空值，替换为 'AAABBBCCC'")
    
    # 确保单据日期为字符串格式（便于分组）
    cleaned_df['单据日期'] = cleaned_df['单据日期'].astype(str)
    
    # 确保数值字段为数值类型，但保留负数
    numeric_fields = ['数量', '销售含税单价', '含税总金额']
    for field in numeric_fields:
        cleaned_df[field] = pd.to_numeric(cleaned_df[field], errors='coerce')
        
        # 检查转换后的空值（不包括负数）
        null_count = cleaned_df[field].isnull().sum()
        if null_count > 0:
            print(f"⚠ 警告: {field} 字段有 {null_count} 个无效数值，将用0填充")
            cleaned_df[field].fillna(0, inplace=True)
            cleaning_log[f'{field}_null_filled'] = null_count
    
    # 记录负数数据（保留不处理）
    negative_qty = (cleaned_df['数量'] < 0).sum()
    negative_amount = (cleaned_df['含税总金额'] < 0).sum()
    cleaning_log['negative_qty_preserved'] = negative_qty
    cleaning_log['negative_amount_preserved'] = negative_amount
    
    if negative_qty > 0:
        print(f"✓ 保留 {negative_qty} 条负数数量记录（退货/调整数据）")
    if negative_amount > 0:
        print(f"✓ 保留 {negative_amount} 条负数金额记录（退货/调整数据）")
    
    print("✓ 数据清洗完成，负数数据已完整保留")
    return cleaned_df, cleaning_log


def perform_enhanced_summary(df):
    """
    执行增强汇总：按单据日期和规格型号分组汇总，添加记录数统计
    
    Args:
        df (pd.DataFrame): 清洗后的数据框
        
    Returns:
        pd.DataFrame: 汇总后的数据框
    """
    print("正在执行增强数据汇总...")
    
    # 按单据日期和规格型号分组，对数值字段求和，并统计记录数
    summary_df = df.groupby(['单据日期', '规格型号']).agg({
        '数量': 'sum',
        '销售含税单价': 'sum', 
        '含税总金额': 'sum',
        '单据编号': 'count'  # 统计每组的记录数
    }).reset_index()
    
    # 重命名字段以明确含义
    summary_df.rename(columns={
        '数量': '汇总数量',
        '销售含税单价': '汇总销售含税单价',
        '含税总金额': '汇总含税总金额',
        '单据编号': '记录数'
    }, inplace=True)
    
    print(f"✓ 汇总完成，原始 {len(df)} 行数据汇总为 {len(summary_df)} 行")
    print(f"✓ 数据压缩率: {(1 - len(summary_df)/len(df))*100:.1f}%")
    
    return summary_df


def validate_summary_balance(original_df, summary_df, quality_report):
    """
    验证汇总数据的平衡性，确保对账准确
    
    Args:
        original_df (pd.DataFrame): 原始数据框
        summary_df (pd.DataFrame): 汇总数据框
        quality_report (dict): 数据质量报告
        
    Returns:
        bool: 验证是否通过
        dict: 验证结果详情
    """
    print("正在进行数据平衡验证...")
    
    validation_result = {}
    
    # 计算汇总后的总金额和总数量
    summary_total_amount = summary_df['汇总含税总金额'].sum()
    summary_total_quantity = summary_df['汇总数量'].sum()
    
    # 与原始数据对比
    original_total_amount = quality_report['total_amount']
    original_total_quantity = quality_report['total_quantity']
    
    # 金额平衡验证（允许微小的浮点误差）
    amount_diff = abs(summary_total_amount - original_total_amount)
    amount_balanced = amount_diff < 0.01  # 允许1分钱的误差
    
    # 数量平衡验证
    quantity_diff = abs(summary_total_quantity - original_total_quantity)
    quantity_balanced = quantity_diff < 0.001  # 允许极小的浮点误差
    
    validation_result['amount_balanced'] = amount_balanced
    validation_result['quantity_balanced'] = quantity_balanced
    validation_result['amount_diff'] = amount_diff
    validation_result['quantity_diff'] = quantity_diff
    validation_result['original_amount'] = original_total_amount
    validation_result['summary_amount'] = summary_total_amount
    validation_result['original_quantity'] = original_total_quantity
    validation_result['summary_quantity'] = summary_total_quantity
    
    # 验证结果
    if amount_balanced and quantity_balanced:
        print("✓ 数据平衡验证通过")
        print(f"  - 金额平衡: 原始 {original_total_amount:,.2f} = 汇总 {summary_total_amount:,.2f}")
        print(f"  - 数量平衡: 原始 {original_total_quantity:,.0f} = 汇总 {summary_total_quantity:,.0f}")
        return True, validation_result
    else:
        print("❌ 数据平衡验证失败")
        if not amount_balanced:
            print(f"  - 金额不平衡: 差异 {amount_diff:,.2f}")
        if not quantity_balanced:
            print(f"  - 数量不平衡: 差异 {quantity_diff:,.3f}")
        return False, validation_result


def export_results(summary_df, output_file='summary_1.xlsx'):
    """
    导出汇总结果到Excel文件

    Args:
        summary_df (pd.DataFrame): 汇总数据框
        output_file (str): 输出文件名
    """
    print(f"正在导出结果到: {output_file}")

    try:
        # 导出到Excel文件
        summary_df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"✓ 成功导出汇总结果到 {output_file}")

        # 显示文件大小信息
        file_size = os.path.getsize(output_file)
        print(f"✓ 文件大小: {file_size:,} 字节")

    except Exception as e:
        raise Exception(f"导出文件时出错: {str(e)}")


def generate_cleaning_report(quality_report, cleaning_log, validation_result, output_file='data_cleaning_report.txt'):
    """
    生成详细的数据清洗报告

    Args:
        quality_report (dict): 数据质量报告
        cleaning_log (dict): 清洗处理记录
        validation_result (dict): 验证结果
        output_file (str): 报告文件名
    """
    print(f"正在生成数据清洗报告: {output_file}")

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("="*60 + "\n")
            f.write("数据清洗汇总报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*60 + "\n\n")

            # 原始数据概况
            f.write("1. 原始数据概况\n")
            f.write("-"*30 + "\n")
            f.write(f"总行数: {quality_report['total_rows']:,}\n")
            f.write(f"总列数: {quality_report['total_columns']}\n")
            f.write(f"数据时间范围: {quality_report['date_range']}\n")
            f.write(f"唯一单据日期数: {quality_report['unique_dates']}\n")
            f.write(f"唯一规格型号数: {quality_report['unique_specs']}\n\n")

            # 数据质量问题
            f.write("2. 数据质量问题\n")
            f.write("-"*30 + "\n")
            f.write(f"规格型号空值: {quality_report['null_spec']} 个\n")
            f.write(f"其他字段空值: {quality_report['null_other']} 个\n")
            f.write(f"完全重复记录: {quality_report['duplicate_rows']} 条\n")
            f.write(f"重复键值记录: {quality_report['duplicate_keys']} 条\n\n")

            # 负数数据分析
            f.write("3. 负数数据分析（退货/调整记录）\n")
            f.write("-"*30 + "\n")
            f.write(f"负数数量记录: {quality_report['negative_qty']} 条\n")
            f.write(f"负数数量范围: {quality_report['negative_qty_range'][0]} 至 {quality_report['negative_qty_range'][1]}\n")
            f.write(f"负数金额记录: {quality_report['negative_amount']} 条\n")
            f.write(f"负数金额范围: {quality_report['negative_amount_range'][0]:,.2f} 至 {quality_report['negative_amount_range'][1]:,.2f}\n\n")

            # 清洗处理记录
            f.write("4. 清洗处理记录\n")
            f.write("-"*30 + "\n")
            if 'spec_null_replaced' in cleaning_log:
                f.write(f"规格型号空值替换: {cleaning_log['spec_null_replaced']} 个 → 'AAABBBCCC'\n")
            f.write(f"负数数量记录保留: {cleaning_log.get('negative_qty_preserved', 0)} 条\n")
            f.write(f"负数金额记录保留: {cleaning_log.get('negative_amount_preserved', 0)} 条\n\n")

            # 汇总结果验证
            f.write("5. 汇总结果验证\n")
            f.write("-"*30 + "\n")
            f.write(f"金额平衡验证: {'通过' if validation_result['amount_balanced'] else '失败'}\n")
            f.write(f"  原始总金额: {validation_result['original_amount']:,.2f}\n")
            f.write(f"  汇总总金额: {validation_result['summary_amount']:,.2f}\n")
            f.write(f"  金额差异: {validation_result['amount_diff']:,.2f}\n\n")
            f.write(f"数量平衡验证: {'通过' if validation_result['quantity_balanced'] else '失败'}\n")
            f.write(f"  原始总数量: {validation_result['original_quantity']:,.0f}\n")
            f.write(f"  汇总总数量: {validation_result['summary_quantity']:,.0f}\n")
            f.write(f"  数量差异: {validation_result['quantity_diff']:,.3f}\n\n")

            # 处理原则说明
            f.write("6. 处理原则说明\n")
            f.write("-"*30 + "\n")
            f.write("• 实事求是原则：完整保留负数数据，确保对账准确性\n")
            f.write("• 负数数据代表退货或调整记录，在汇总时正负相抵\n")
            f.write("• 规格型号空值统一替换为'AAABBBCCC'便于识别\n")
            f.write("• 重复记录通过分组汇总自动处理\n")
            f.write("• 汇总前后数据必须完全平衡，确保无遗漏无重复\n\n")

            f.write("="*60 + "\n")
            f.write("报告结束\n")
            f.write("="*60 + "\n")

        print(f"✓ 数据清洗报告已生成: {output_file}")

    except Exception as e:
        print(f"⚠ 生成报告时出错: {str(e)}")


def display_enhanced_preview(summary_df, original_df, quality_report, validation_result):
    """
    在控制台显示增强的汇总结果预览和统计信息

    Args:
        summary_df (pd.DataFrame): 汇总数据框
        original_df (pd.DataFrame): 原始数据框
        quality_report (dict): 数据质量报告
        validation_result (dict): 验证结果
    """
    print("\n" + "="*80)
    print("增强型汇总结果预览")
    print("="*80)

    # 显示前10行汇总结果
    print("\n前10行汇总数据:")
    print("-" * 80)
    print(summary_df.head(10).to_string(index=False))

    print("\n" + "="*80)
    print("详细统计信息")
    print("="*80)

    # 基础统计
    print(f"\n📊 数据规模统计:")
    print(f"原始数据行数: {len(original_df):,}")
    print(f"汇总后行数: {len(summary_df):,}")
    print(f"数据压缩率: {(1 - len(summary_df)/len(original_df))*100:.1f}%")
    print(f"数据时间跨度: {quality_report['date_range']}")

    # 负数数据统计
    print(f"\n🔄 负数数据统计（退货/调整）:")
    print(f"负数数量记录: {quality_report['negative_qty']} 条")
    print(f"负数金额记录: {quality_report['negative_amount']} 条")
    if quality_report['negative_qty'] > 0:
        print(f"负数数量范围: {quality_report['negative_qty_range'][0]} 至 {quality_report['negative_qty_range'][1]}")
        print(f"负数金额范围: {quality_report['negative_amount_range'][0]:,.2f} 至 {quality_report['negative_amount_range'][1]:,.2f}")

    # 汇总数值统计
    print(f"\n💰 汇总数值统计:")
    print(f"汇总总数量: {summary_df['汇总数量'].sum():,.0f}")
    print(f"汇总总销售含税单价: {summary_df['汇总销售含税单价'].sum():,.2f}")
    print(f"汇总总含税金额: {summary_df['汇总含税总金额'].sum():,.2f}")

    # 数据平衡验证结果
    print(f"\n✅ 数据平衡验证:")
    print(f"金额平衡: {'✓ 通过' if validation_result['amount_balanced'] else '❌ 失败'}")
    print(f"数量平衡: {'✓ 通过' if validation_result['quantity_balanced'] else '❌ 失败'}")

    # 分组统计
    print(f"\n📈 分组统计:")
    print(f"不同单据日期数: {summary_df['单据日期'].nunique()}")
    print(f"不同规格型号数: {summary_df['规格型号'].nunique()}")
    print(f"平均每组记录数: {summary_df['记录数'].mean():.1f}")
    print(f"最大组记录数: {summary_df['记录数'].max()}")

    # 数据质量评分
    quality_score = 100
    if quality_report['null_spec'] > 0:
        quality_score -= 5
    if quality_report['negative_qty'] > len(original_df) * 0.1:  # 负数超过10%扣分
        quality_score -= 10
    if quality_report['duplicate_rows'] > 0:
        quality_score -= 5

    print(f"\n🎯 数据质量评分: {quality_score}/100")


def main():
    """
    主函数：整合所有功能模块，执行增强型数据清洗汇总
    """
    print("="*80)
    print("增强型Excel数据清洗汇总脚本")
    print("开发作者: 学文")
    print("开发时间: 2025-01-15")
    print("处理原则: 实事求是，确保对账准确性")
    print("="*80)

    try:
        # 1. 加载和验证数据
        original_df = load_and_validate_data('1.xlsx')

        # 2. 数据质量分析
        quality_report = analyze_data_quality(original_df)

        # 3. 数据清洗处理
        cleaned_df, cleaning_log = clean_data(original_df, quality_report)

        # 4. 执行增强汇总
        summary_df = perform_enhanced_summary(cleaned_df)

        # 5. 数据平衡验证
        balance_passed, validation_result = validate_summary_balance(original_df, summary_df, quality_report)

        if not balance_passed:
            print("❌ 数据平衡验证失败，停止处理以确保数据准确性")
            sys.exit(1)

        # 6. 导出结果
        export_results(summary_df, 'summary_1.xlsx')

        # 7. 生成清洗报告
        generate_cleaning_report(quality_report, cleaning_log, validation_result)

        # 8. 显示增强预览
        display_enhanced_preview(summary_df, original_df, quality_report, validation_result)

        print("\n" + "="*80)
        print("✅ 增强型数据清洗汇总任务完成！")
        print("✅ 汇总结果已保存到 summary_1.xlsx")
        print("✅ 清洗报告已保存到 data_cleaning_report.txt")
        print("✅ 数据平衡验证通过，对账准确性得到保证")
        print("="*80)

    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {str(e)}")
        print("请检查输入文件和数据格式")
        sys.exit(1)


if __name__ == "__main__":
    main()
