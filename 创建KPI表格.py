# 开发作者: 学文
# 开发时间: 2025-01-16
# 模块说明: 创建带公式的KPI考核表Excel文件

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

def create_kpi_excel_with_formulas():
    """创建带公式的KPI Excel表格"""
    
    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "9月KPI考核表"
    
    # 表头
    headers = ['类别', '项目名称', '目标值', '实际值', '完成率(%)', '分值', '得分', '备注']
    
    # KPI数据
    kpi_data = [
        ['销售业绩指标', '收购龙清单', 5, '', '=IF(D2="","",D2/C2*100)', 7, '=IF(E2="","",MIN(E2/100*F2,F2))', '基于历史数据设定'],
        ['销售业绩指标', '收购龙客户数量/万元', 1.5, '', '=IF(D3="","",D3/C3*100)', 14, '=IF(E3="","",MIN(E3/100*F3,F3))', '1.5万元合理目标'],
        ['客户开发指标', '新客户开发加人', 4, '', '=IF(D4="","",D4/C4*100)', 12, '=IF(E4="","",MIN(E4/100*F4,F4))', '月均新客户开发'],
        ['客户开发指标', '收购龙客户数量/万人', 4, '', '=IF(D5="","",D5/C5*100)', 0, 0, '此项不计分'],
        ['库存与销售指标', '收购龙销售额/万元', 72, '', '=IF(D6="","",D6/C6*100)', 15, '=IF(E6="","",MIN(E6/100*F6,F6))', '基于历史72万+5%增长'],
        ['库存与销售指标', '库存个人占比/万元', 2, '', '=IF(D7="","",D7/C7*100)', 2, '=IF(E7="","",MIN(E7/100*F7,F7))', '控制库存成本'],
        ['库存与销售指标', '产品个人占比/万元', 2, '', '=IF(D8="","",D8/C8*100)', 2, '=IF(E8="","",MIN(E8/100*F8,F8))', '产品占比控制'],
        ['库存与销售指标', '平台工单处理', 2, '', '=IF(D9="","",D9/C9*100)', 2, '=IF(E9="","",MIN(E9/100*F9,F9))', '工单及时处理'],
        ['库存与销售指标', '客户投诉', 2, '', '=IF(D10="","",IF(D10<=C10,100,D10/C10*100))', 2, '=IF(E10="","",IF(D10<=C10,F10,0))', '越少越好'],
        ['欠款控制指标', '30天欠款/万元', 2, '', '=IF(D11="","",IF(D11<=C11,100,D11/C11*100))', 2, '=IF(E11="","",IF(D11<=C11,F11,0))', '控制在2万内得满分'],
        ['欠款控制指标', '60天欠款/万元', 2, '', '=IF(D12="","",IF(D12<=C12,100,D12/C12*100))', 8, '=IF(E12="","",IF(D12<=C12,F12,0))', '控制在2万内得满分'],
        ['欠款控制指标', '90天欠款/万元', 2, '', '=IF(D13="","",IF(D13<=C13,100,D13/C13*100))', 0, 0, '此项不计分']
    ]
    
    # 写入表头
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # 写入数据
    for row_idx, row_data in enumerate(kpi_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            if col_idx == 1:  # 类别列
                cell.fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
            elif col_idx in [3, 6]:  # 目标值和分值列
                cell.fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
            elif col_idx == 4:  # 实际值列
                cell.fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
    
    # 添加总分行
    total_row = len(kpi_data) + 3
    ws.cell(row=total_row, column=1, value="总计").font = Font(bold=True)
    ws.cell(row=total_row, column=6, value="总分值:").font = Font(bold=True)
    ws.cell(row=total_row, column=7, value=f"=SUM(F2:F{len(kpi_data)+1})").font = Font(bold=True)
    ws.cell(row=total_row+1, column=6, value="实际得分:").font = Font(bold=True)
    ws.cell(row=total_row+1, column=7, value=f"=SUM(G2:G{len(kpi_data)+1})").font = Font(bold=True)
    ws.cell(row=total_row+2, column=6, value="完成率:").font = Font(bold=True)
    ws.cell(row=total_row+2, column=7, value=f"=G{total_row+1}/G{total_row}*100&\"%\"").font = Font(bold=True)
    
    # 设置列宽
    column_widths = [15, 20, 10, 10, 12, 8, 8, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64+i)].width = width
    
    # 添加边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.iter_rows(min_row=1, max_row=len(kpi_data)+1, min_col=1, max_col=len(headers)):
        for cell in row:
            cell.border = thin_border
    
    # 保存文件
    wb.save('9月KPI考核表_带公式.xlsx')
    print("✅ 带公式的KPI表格已创建: 9月KPI考核表_带公式.xlsx")
    
    return wb

def create_simple_table():
    """创建简单的填写模板"""
    
    # 创建简单的DataFrame
    data = {
        '项目': [
            '收购龙清单', '收购龙客户数量(万元)', '新客户开发(个)', 
            '销售额(万元)', '库存占比(万元)', '30天欠款(万元)', 
            '60天欠款(万元)', '客户投诉(次)', '工单处理(个)'
        ],
        '目标值': [5, 1.5, 4, 72, 2, 2, 2, 2, 2],
        '实际值': [''] * 9,
        '完成情况': [''] * 9,
        '备注': [
            '重点客户清单', '重要客户金额', '新开发客户数', 
            '月度销售总额', '个人库存控制', '30天内回款', 
            '60天内回款', '客户投诉次数', '处理工单数量'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_excel('KPI填写模板.xlsx', index=False)
    print("✅ 简化版KPI模板已创建: KPI填写模板.xlsx")
    
    return df

if __name__ == "__main__":
    print("🚀 开始创建KPI表格...")
    print()
    
    # 创建带公式的完整表格
    create_kpi_excel_with_formulas()
    
    # 创建简化版模板
    create_simple_table()
    
    print()
    print("📋 使用说明:")
    print("1. '9月KPI考核表_带公式.xlsx' - 完整版，包含自动计算公式")
    print("2. 'KPI填写模板.xlsx' - 简化版，便于快速填写")
    print()
    print("💡 填写提示:")
    print("• 在'实际值'列填入9月底的实际完成数据")
    print("• 完成率和得分会自动计算")
    print("• 销售额目标72万元是基于历史数据+5%增长")
    print("• 欠款控制项目：实际值≤目标值时得满分")
