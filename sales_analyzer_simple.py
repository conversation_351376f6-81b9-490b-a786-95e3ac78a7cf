#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
销售数据分析脚本（简化版）
功能：分析2.xlsx文件中的销售数据，无需matplotlib依赖
作者：学文
开发时间：2025-01-16
模块说明：销售数据透视分析工具（简化版）
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
import os
import sys

warnings.filterwarnings('ignore')

class SalesDataAnalyzer:
    """销售数据分析器（简化版）"""
    
    def __init__(self, file_path='2.xlsx'):
        """初始化分析器"""
        self.file_path = file_path
        self.raw_data = None
        self.clean_data = None
        self.monthly_analysis = None
        
    def load_data(self):
        """加载和预处理数据"""
        try:
            print("正在加载数据...")
            self.raw_data = pd.read_excel(self.file_path, engine='openpyxl')
            print(f"数据加载成功！共{len(self.raw_data)}行，{len(self.raw_data.columns)}列")
            
            # 重命名列名
            if len(self.raw_data.columns) >= 8:
                self.raw_data.columns = [
                    'Date', 'Customer', 'Product_Type', 'Model', 
                    'Brand', 'Quantity', 'Unit_Price', 'Total_Amount'
                ]
            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def clean_and_process_data(self):
        """清洗和验证数据"""
        try:
            print("正在清洗数据...")
            self.clean_data = self.raw_data.copy()
            
            # 过滤有效日期（保持原始数据完整性）
            date_pattern = r'^\d{4}-\d{2}-\d{2}$'
            valid_dates = self.clean_data['Date'].astype(str).str.match(date_pattern, na=False)
            self.clean_data = self.clean_data[valid_dates].copy()

            # 转换数据类型
            self.clean_data['Date'] = pd.to_datetime(self.clean_data['Date'])
            self.clean_data['Quantity'] = pd.to_numeric(self.clean_data['Quantity'], errors='coerce')
            self.clean_data['Unit_Price'] = pd.to_numeric(self.clean_data['Unit_Price'], errors='coerce')
            self.clean_data['Total_Amount'] = pd.to_numeric(self.clean_data['Total_Amount'], errors='coerce')

            # 只移除明显的数据错误（空值），保留所有真实交易数据
            self.clean_data = self.clean_data.dropna(subset=['Total_Amount'])

            # 添加月份字段
            self.clean_data['Month'] = self.clean_data['Date'].dt.to_period('M')
            self.clean_data['Month_Name'] = self.clean_data['Date'].dt.strftime('%Y年%m月')

            print(f"数据处理完成！有效数据{len(self.clean_data)}行")
            print(f"总销售额（含税）：{self.clean_data['Total_Amount'].sum():,.2f}元")
            return True
        except Exception as e:
            print(f"数据清洗失败：{str(e)}")
            return False
    
    def analyze_monthly_data(self):
        """按月份和产品系列进行数据透视分析"""
        try:
            print("正在进行月度数据分析...")
            
            # 按月份和产品系列分组统计
            self.monthly_analysis = self.clean_data.groupby(['Month_Name', 'Product_Type']).agg({
                'Quantity': 'sum',
                'Total_Amount': 'sum',
                'Unit_Price': 'mean'
            }).round(2)
            
            self.monthly_analysis.columns = ['销售数量', '销售金额', '平均单价']
            
            # 月度汇总
            monthly_totals = self.clean_data.groupby('Month_Name').agg({
                'Quantity': 'sum',
                'Total_Amount': 'sum',
                'Product_Type': 'nunique'
            }).round(2)
            monthly_totals.columns = ['总销售数量', '总销售金额', '产品系列数']
            self.monthly_totals = monthly_totals
            
            print("月度数据分析完成！")
            return True
        except Exception as e:
            print(f"月度数据分析失败：{str(e)}")
            return False
    
    def print_analysis_results(self):
        """打印分析结果"""
        print("\n" + "="*60)
        print("           销售数据分析报告")
        print("="*60)
        
        # 总体概况
        print(f"\n【数据概况】")
        print(f"分析期间：{self.clean_data['Date'].min().strftime('%Y-%m-%d')} 至 {self.clean_data['Date'].max().strftime('%Y-%m-%d')}")
        print(f"总销售金额：{self.clean_data['Total_Amount'].sum():,.2f}元")
        print(f"总订单数：{len(self.clean_data)}单")
        print(f"产品系列数：{self.clean_data['Product_Type'].nunique()}个")
        print(f"客户数量：{self.clean_data['Customer'].nunique()}个")
        
        # 月度分析
        print(f"\n【月度总体分析】")
        for month in self.monthly_totals.index:
            data = self.monthly_totals.loc[month]
            print(f"{month}：")
            print(f"  - 总销售金额：{data['总销售金额']:,.2f}元")
            print(f"  - 总销售数量：{data['总销售数量']:,.0f}件")
            print(f"  - 产品系列数：{data['产品系列数']}个")
        
        # 详细分析（只显示前5个产品系列）
        print(f"\n【按月份和产品系列详细分析】（显示前5个系列）")
        for month in self.monthly_analysis.index.get_level_values(0).unique():
            print(f"\n{month}销售分析：")
            month_data = self.monthly_analysis.loc[month]
            month_data_sorted = month_data.sort_values('销售金额', ascending=False)
            
            for i, (product_type, data) in enumerate(month_data_sorted.head(5).iterrows()):
                print(f"  {product_type}：")
                print(f"    - 销售数量：{data['销售数量']:,.0f}件")
                print(f"    - 销售金额：{data['销售金额']:,.2f}元")
                print(f"    - 平均单价：{data['平均单价']:,.2f}元")
    
    def query_specific_data(self, month=None, product_type=None):
        """查询指定月份和系列的数据"""
        try:
            if month and product_type:
                result = self.monthly_analysis.loc[(month, product_type)]
                print(f"\n【{month} - {product_type} 详细数据】")
                print(f"销售数量：{result['销售数量']:,.0f}件")
                print(f"销售金额：{result['销售金额']:,.2f}元")
                print(f"平均单价：{result['平均单价']:,.2f}元")
            elif month:
                result = self.monthly_analysis.loc[month]
                print(f"\n【{month} 所有产品系列数据】")
                result_sorted = result.sort_values('销售金额', ascending=False)
                for product in result_sorted.index:
                    data = result_sorted.loc[product]
                    print(f"{product}：数量{data['销售数量']:,.0f}件，金额{data['销售金额']:,.2f}元")
            else:
                print("请指定查询的月份或产品系列")
        except KeyError:
            print("未找到指定的数据，请检查月份和产品系列名称")
        except Exception as e:
            print(f"查询失败：{str(e)}")
    
    def calculate_market_share(self):
        """计算各系列的市场占有率"""
        try:
            print("\n【产品系列市场占有率分析】")
            product_totals = self.clean_data.groupby('Product_Type')['Total_Amount'].sum().sort_values(ascending=False)
            total_sales = product_totals.sum()
            
            print("按销售金额排名（前10名）：")
            for i, (product, amount) in enumerate(product_totals.head(10).items(), 1):
                share = (amount / total_sales) * 100
                print(f"{i:2d}. {product}：{amount:,.2f}元 ({share:.1f}%)")
        except Exception as e:
            print(f"市场占有率计算失败：{str(e)}")
    
    def generate_pivot_table(self):
        """生成交叉统计表"""
        try:
            print("\n【数据透视表】")
            
            # 销售金额透视表
            pivot_amount = pd.pivot_table(
                self.clean_data, 
                values='Total_Amount', 
                index='Product_Type', 
                columns='Month_Name', 
                aggfunc='sum', 
                fill_value=0
            ).round(2)
            
            print("销售金额透视表（前10个产品系列）：")
            print(pivot_amount.head(10))
            
            # 保存透视表
            self.pivot_amount = pivot_amount
            return True
        except Exception as e:
            print(f"透视表生成失败：{str(e)}")
            return False
    
    def export_results(self, output_file='销售分析结果.xlsx'):
        """导出分析结果到Excel文件"""
        try:
            print(f"\n正在导出结果到 {output_file}...")
            
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 导出月度分析数据
                self.monthly_analysis.to_excel(writer, sheet_name='月度产品分析')
                
                # 导出月度汇总数据
                self.monthly_totals.to_excel(writer, sheet_name='月度汇总')
                
                # 导出产品系列汇总
                product_summary = self.clean_data.groupby('Product_Type').agg({
                    'Quantity': 'sum',
                    'Total_Amount': 'sum',
                    'Unit_Price': 'mean'
                }).round(2)
                product_summary.columns = ['总销售数量', '总销售金额', '平均单价']
                product_summary.to_excel(writer, sheet_name='产品系列汇总')
                
                # 导出透视表
                if hasattr(self, 'pivot_amount'):
                    self.pivot_amount.to_excel(writer, sheet_name='销售金额透视表')
                
                # 导出原始数据
                self.clean_data.to_excel(writer, sheet_name='清洗后数据', index=False)
            
            print(f"结果导出成功：{output_file}")
        except Exception as e:
            print(f"导出失败：{str(e)}")
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始销售数据分析...")
        
        if not os.path.exists(self.file_path):
            print(f"错误：文件 {self.file_path} 不存在！")
            return False
        
        # 执行分析步骤
        if not self.load_data():
            return False
        if not self.clean_and_process_data():
            return False
        if not self.analyze_monthly_data():
            return False
        
        # 输出分析结果
        self.print_analysis_results()
        self.calculate_market_share()
        self.generate_pivot_table()
        self.export_results()
        
        print("\n分析完成！")
        return True

def main():
    """主函数"""
    print("="*60)
    print("           销售数据分析工具（简化版）")
    print("="*60)
    
    # 创建分析器实例
    analyzer = SalesDataAnalyzer('2.xlsx')
    
    # 运行分析
    if analyzer.run_analysis():
        print("\n" + "="*60)
        print("分析完成！您可以使用以下功能：")
        print("="*60)
        
        # 显示可用查询选项
        if hasattr(analyzer, 'monthly_analysis') and analyzer.monthly_analysis is not None:
            available_months = analyzer.monthly_analysis.index.get_level_values(0).unique()
            available_products = analyzer.monthly_analysis.index.get_level_values(1).unique()
            
            print(f"\n可查询月份：{list(available_months)}")
            print(f"产品系列数：{len(available_products)}个")
            
            # 示例查询
            if len(available_months) > 0:
                sample_month = available_months[0]
                print(f"\n【示例查询 - {sample_month}】")
                analyzer.query_specific_data(sample_month)
        
        print(f"\n生成文件：销售分析结果.xlsx")
        print("请查看Excel文件获取详细数据！")

if __name__ == "__main__":
    main()
