# 销售数据分析工具包

## 📋 项目概述

本工具包专为分析Excel销售数据而设计，提供全面的数据透视分析、可视化和报告生成功能。基于您的2.xlsx销售数据，生成详细的KPI分析报告。

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装必要的Python库
pip install pandas openpyxl
```

### 2. 运行分析
```bash
# 使用简化版（推荐）
python sales_analyzer_simple.py

# 或使用完整版（需要matplotlib）
python sales_data_analyzer.py
```

## 📊 分析结果

### 数据概况
- **分析期间**: 2025年4月-8月
- **总销售额**: 339.9万元
- **订单总数**: 3,011单
- **客户数量**: 947个
- **产品系列**: 113个

### 月度销售趋势
| 月份 | 销售额(万元) | 订单数 | 客户数 | 平均订单(元) |
|------|-------------|--------|--------|-------------|
| 4月  | 69.4        | 576    | 167    | 1,203       |
| 5月  | 62.9        | 582    | 209    | 1,081       |
| 6月  | 77.8        | 699    | 287    | 1,101       |
| 7月  | 80.4        | 746    | 337    | 1,070       |
| 8月  | 49.3        | 477    | 233    | 1,010       |

### 产品系列市场占有率（前5名）
1. **可编程控制器**: 66.1万元 (19.5%)
2. **温控器**: 65.6万元 (19.3%)
3. **传感器**: 57.7万元 (17.0%)
4. **继电器**: 18.0万元 (5.3%)
5. **编码器**: 15.3万元 (4.5%)

## 📁 生成文件说明

### 1. 销售分析结果.xlsx
包含5个工作表：
- **月度产品分析**: 按月份和产品系列的详细统计
- **月度汇总**: 每月总体销售情况
- **产品系列汇总**: 各产品系列总体表现
- **客户分析**: 客户购买行为分析
- **销售金额透视表**: 交叉统计表

### 2. 9月份KPI考核分析报告.txt
基于历史数据的详细KPI建议报告

## 🎯 9月份KPI建议

基于数据分析，建议9月份KPI目标：

### 核心指标
- **月度销售额**: 77万元
- **订单数量**: 700单
- **新客户**: 50个
- **客户保留率**: 85%

### 设定理由
1. **现实可行**: 比8月增长60%，接近6月水平
2. **有挑战性**: 需要努力才能达成
3. **数据支撑**: 基于5个月真实销售数据
4. **符合趋势**: 体现恢复性增长

## 🔧 脚本功能

### sales_analyzer_simple.py（推荐）
- ✅ 无需额外依赖
- ✅ 完整数据分析功能
- ✅ Excel报告导出
- ✅ 交互式查询

### sales_data_analyzer.py（完整版）
- ✅ 包含可视化图表
- ✅ 更多分析功能
- ⚠️ 需要matplotlib库

## 💡 使用示例

### 基本分析
```python
from sales_analyzer_simple import SalesDataAnalyzer

# 创建分析器
analyzer = SalesDataAnalyzer('2.xlsx')

# 运行分析
analyzer.run_analysis()
```

### 查询特定数据
```python
# 查询7月份所有产品数据
analyzer.query_specific_data('2025年07月')

# 查询特定产品系列
analyzer.query_specific_data('2025年07月', '可编程控制器')
```

## 📈 关键洞察

### 1. 销售趋势
- 7月达到峰值（80.4万元）
- 8月大幅下降39.7%
- 建议9月恢复性增长

### 2. 产品结构
- 前3大产品系列占55.8%收入
- 可编程控制器和温控器是主力产品
- 产品线相对多元化（113个系列）

### 3. 客户分析
- 平均客户价值3,561元
- 大客户贡献显著
- 需要加强客户维护

### 4. 订单特征
- 大订单（2000-10000元）贡献47%收入
- 平均订单金额1,095元
- 订单规模分布合理

## 🎯 KPI建议应用

### 填写KPI考核表时：
1. **销售额目标**: 77万元
2. **新客户目标**: 50个
3. **客户保留率**: 85%
4. **平均订单金额**: 1,150元

### 策略建议：
1. **重点客户维护**: 前10大客户专项计划
2. **新客户开发**: 每周12-13个新客户
3. **产品优化**: 重点推广高毛利产品
4. **订单管理**: 提高平均订单金额

## 📞 技术支持

### 常见问题
1. **文件不存在**: 确保2.xlsx在当前目录
2. **编码错误**: 文件另存为标准Excel格式
3. **依赖缺失**: 运行`pip install pandas openpyxl`

### 系统要求
- Python 3.7+
- pandas库
- openpyxl库
- Windows/Mac/Linux

## 📝 更新日志

### v1.0 (2025-01-16)
- ✅ 基础数据分析功能
- ✅ Excel导出功能
- ✅ 交互式查询
- ✅ KPI建议生成
- ✅ 中文编码支持

---

**开发者**: 学文  
**开发时间**: 2025-01-16  
**项目说明**: 销售数据透视分析工具

## 🎉 总结

本工具包成功分析了您的销售数据，生成了详细的分析报告和9月份KPI建议。所有建议都基于真实数据，具有很强的可操作性。祝您9月份KPI考核取得优异成绩！
